#[==============================================[
FindLibUSB
-----------

Searching libusb-1.0 library and creating imported 
target LibUSB::LibUSB

#]==============================================]

# TODO Append parts for Version compasion and REQUIRED support

# Support Windows builds with vcpkg
if (MSVC OR MINGW)
    # Try to find libusb via vcpkg first
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(LibUSB QUIET libusb-1.0)
    endif()

    # If pkg-config didn't work, try find_package
    if(NOT LibUSB_FOUND)
        find_package(libusb-1.0 CONFIG QUIET)
        if(libusb-1.0_FOUND)
            set(LibUSB_FOUND TRUE)
            if(NOT TARGET LibUSB::LibUSB)
                add_library(LibUSB::LibUSB ALIAS libusb-1.0)
            endif()
            return()
        endif()
    endif()

    # If still not found, try manual search
    if(NOT LibUSB_FOUND)
        find_path(LibUSB_INCLUDE_DIR
            NAMES libusb.h
            PATH_SUFFIXES libusb-1.0
        )
        find_library(LibUSB_LIBRARY
            NAMES usb-1.0 libusb-1.0
        )

        if(LibUSB_INCLUDE_DIR AND LibUSB_LIBRARY)
            set(LibUSB_FOUND TRUE)
            set(LibUSB_INCLUDE_DIRS ${LibUSB_INCLUDE_DIR})
            set(LibUSB_LIBRARIES ${LibUSB_LIBRARY})

            if(NOT TARGET LibUSB::LibUSB)
                add_library(LibUSB::LibUSB UNKNOWN IMPORTED)
                set_target_properties(LibUSB::LibUSB PROPERTIES
                    INTERFACE_INCLUDE_DIRECTORIES "${LibUSB_INCLUDE_DIRS}"
                    IMPORTED_LOCATION "${LibUSB_LIBRARY}"
                    IMPORTED_LINK_INTERFACE_LANGUAGES "C"
                )
            endif()
            return()
        endif()
    endif()

    # If we reach here, libusb was not found
    if(NOT LibUSB_FOUND)
        message(WARNING "libusb-1.0 could not be found on Windows. Please install it via vcpkg: vcpkg install libusb")
        return()
    endif()
endif()

if (NOT TARGET LibUSB::LibUSB)
  find_package(PkgConfig)
  pkg_check_modules(LibUSB REQUIRED
    libusb-1.0
  )

  if(LibUSB_FOUND)
    message(STATUS "libusb-1.0 found using pkgconfig")

    add_library(LibUSB::LibUSB
      UNKNOWN IMPORTED
    )
    if (DEFINED LibUSB_INCLUDE_DIRS AND NOT LibUSB_INCLUDE_DIRS STREQUAL "")
      set_target_properties(LibUSB::LibUSB PROPERTIES
        INTERFACE_INCLUDE_DIRECTORIES ${LibUSB_INCLUDE_DIRS}
      )
    endif()

    if(LibUSB_LIBRARIES)
      find_library(LibUSB_LIBRARY
        NAMES ${LibUSB_LIBRARIES}
        PATHS ${LibUSB_LIBDIR} ${LibUSB_LIBRARY_DIRS}
      )
      if(LibUSB_LIBRARY)
        set_target_properties(LibUSB::LibUSB PROPERTIES
          IMPORTED_LINK_INTERFACE_LANGUAGES "C"
          IMPORTED_LOCATION ${LibUSB_LIBRARY}
        )
      else()
        message(WARNING "Could not found libusb-1.0 library file")
      endif()
    endif()
  endif()
else()
  message(WARNING "libusb-1.0 could not be found using pkgconfig")
endif()
